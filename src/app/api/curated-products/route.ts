import { NextRequest, NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { extractSpecificIngredients } from '../../../lib/recommendation-utils';

// Initialize Supabase client with service role key for server-side operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabase: SupabaseClient | null = null;
if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey, {
    db: { schema: 'public' },
  });
}

// Define attractiveness factors and their corresponding semantic keywords
const ATTRACTIVENESS_KEYWORD_MAPPING: Record<string, string[]> = {
  'skin_radiance': ['skin_radiance', 'vitamin_c_glow', 'skin_brightness'],
  'skin_elasticity': ['skin_elasticity', 'collagen', 'hydration'],
  'bone_structure': ['bone_structure', 'facial_structure'],
  'eye_vitality': ['eye_vitality', 'under_eye_care'],
  'hair_health': ['hair_health', 'hair_growth', 'hair_strength'],
  'facial_symphony': ['facial_symphony', 'skin_radiance', 'bone_structure'],
  'beard_care': ['beard_care', 'facial_hair_grooming', 'masculine_grooming'],
  'hydration': ['hydration', 'skin_elasticity', 'hyaluronic_acid'],
  'vitamin_c_glow': ['vitamin_c_glow', 'skin_radiance', 'antioxidant'],
  'collagen': ['collagen', 'skin_elasticity', 'anti_aging'],
  'skin_brightness': ['skin_brightness', 'skin_radiance', 'niacinamide'],
  'skin_protection': ['skin_protection', 'spf', 'sunscreen'],
  'skin_cleansing': ['skin_cleansing', 'cleanser', 'facial_wash'],
  'skin_texture': ['skin_texture', 'exfoliant', 'skin_smoothing'],
  'undefined': ['skin_radiance', 'vitamin_c_glow', 'skin_brightness']
};

// Extract attractiveness factors from analysis recommendations
function extractAttractivenessFactors(recommendations: string): string[] {
  const lowerText = recommendations.toLowerCase();
  const factors: string[] = [];

  // Map analysis text to attractiveness factors
  if (lowerText.includes('skin') || lowerText.includes('hydration') || lowerText.includes('texture')) {
    factors.push('skin_radiance', 'skin_elasticity');
  }

  if (lowerText.includes('bone') || lowerText.includes('structure') || lowerText.includes('facial') || lowerText.includes('jaw')) {
    factors.push('bone_structure');
  }

  if (lowerText.includes('eye') || lowerText.includes('vision') || lowerText.includes('ocular')) {
    factors.push('eye_vitality');
  }

  if (lowerText.includes('hair') || lowerText.includes('growth') || lowerText.includes('thickness')) {
    factors.push('hair_health');
  }

  // Only include beard care if specifically mentioned (indicates visible facial hair)
  if (lowerText.includes('beard') || lowerText.includes('facial hair') || lowerText.includes('mustache') || lowerText.includes('goatee')) {
    factors.push('beard_care');
  }

  if (lowerText.includes('symmetry') || lowerText.includes('proportional')) {
    factors.push('facial_symphony');
  }

  // Extract specific ingredients and map them to factors
  const specificIngredients = extractSpecificIngredients(recommendations);
  specificIngredients.forEach(ingredient => {
    switch (ingredient.toLowerCase()) {
      case 'hyaluronic acid':
      case 'moisturizer':
        factors.push('hydration', 'skin_elasticity');
        break;
      case 'vitamin c':
      case 'antioxidants':
        factors.push('skin_radiance', 'vitamin_c_glow');
        break;
      case 'retinol':
      case 'peptides':
      case 'collagen':
        factors.push('collagen', 'skin_elasticity');
        break;
      case 'niacinamide':
      case 'serum':
        factors.push('skin_radiance', 'skin_brightness');
        break;
      case 'spf':
      case 'sunscreen':
        factors.push('skin_protection');
        break;
      case 'cleanser':
        factors.push('skin_cleansing');
        break;
      case 'exfoliant':
        factors.push('skin_texture');
        break;
    }
  });

  // Default factors if none matched
  if (factors.length === 0) {
    factors.push('skin_radiance', 'bone_structure', 'eye_vitality');
  }

  // Remove duplicates
  return Array.from(new Set(factors));
}

// Get relevant product keywords based on attractiveness factors
function getRelevantKeywords(factors: string[]): string[] {
  const keywords: string[] = [];
  const keywordSet = new Set<string>();

  factors.forEach(factor => {
    const factorKeywords = ATTRACTIVENESS_KEYWORD_MAPPING[factor] || [];
    factorKeywords.forEach(keyword => keywordSet.add(keyword));
  });

  // Convert set to array
  const uniqueKeywords: string[] = [];
  keywordSet.forEach(keyword => uniqueKeywords.push(keyword));
  return uniqueKeywords;
}

export async function POST(request: NextRequest) {
  try {
    const { recommendations } = await request.json();

    if (!recommendations || typeof recommendations !== 'string') {
      return NextResponse.json(
        { error: 'Recommendations text is required' },
        { status: 400 }
      );
    }

    // Extract attractiveness factors from recommendations
    const factors = extractAttractivenessFactors(recommendations);
    const keywords = getRelevantKeywords(factors);

    console.log('DEBUG: Extracted factors:', factors);
    console.log('DEBUG: Generated keywords:', keywords);

    // Check if Supabase is configured
    if (!supabase) {
      // Return sample products for demo purposes when database isn't configured
      // Filter products based on specific ingredients mentioned in recommendations
      const specificIngredients = extractSpecificIngredients(recommendations);
      let sampleProducts = [
        {
          asin: 'DEMO-001',
          title: 'La Merck Vitamin C Serum for Brightening & Anti-Aging, 1 oz',
          imageUrl: 'https://m.media-amazon.com/images/I/61-LYQGtQCL._AC_UL320_.jpg',
          price: '$29.99',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 1248,
          affiliateUrl: `https://www.amazon.com/dp/B07WZVX3L1?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_radiance', 'vitamin_c_glow', 'skin_brightness'],
          isDiscounted: false,
          ingredients: ['vitamin c', 'antioxidants']
        },
        {
          asin: 'DEMO-002',
          title: 'The Ordinary Hyaluronic Acid 2% + B5 Serum, 30ml',
          imageUrl: 'https://m.media-amazon.com/images/I/71ZBnxvlvCL._AC_UL320_.jpg',
          price: '$7.20',
          priceBeforeDiscount: '$9.99',
          rating: null,
          reviewCount: 1586,
          affiliateUrl: `https://www.amazon.com/dp/B01NBTJFJB?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_elasticity', 'hydration', 'facial_contouring'],
          isDiscounted: true,
          ingredients: ['hyaluronic acid']
        },
        {
          asin: 'DEMO-003',
          title: 'CeraVe Moisturizing Cream with Ceramides and Hyaluronic Acid, 1.7 oz',
          imageUrl: 'https://m.media-amazon.com/images/I/514DekriBLL._AC_UL320_.jpg',
          price: '$18.99',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 2856,
          affiliateUrl: `https://www.amazon.com/dp/B07C7S9Q4F?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_radiance', 'hydration', 'skin_firmness'],
          isDiscounted: false,
          ingredients: ['hyaluronic acid', 'ceramides']
        },
        {
          asin: 'DEMO-004',
          title: 'Olay Regenerist Micro-Sculpting Cream with Niacinamide, 1.7 oz',
          imageUrl: 'https://m.media-amazon.com/images/I/71QvzJGzJeL._AC_UL320_.jpg',
          price: '$24.99',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 3421,
          affiliateUrl: `https://www.amazon.com/dp/B00NR1YQK4?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_brightness', 'skin_radiance', 'anti_aging'],
          isDiscounted: false,
          ingredients: ['niacinamide', 'peptides']
        },
        {
          asin: 'DEMO-005',
          title: 'Neutrogena Ultra Sheer Dry-Touch Sunscreen SPF 100+, 3 oz',
          imageUrl: 'https://m.media-amazon.com/images/I/71VvzQGzJeL._AC_UL320_.jpg',
          price: '$8.97',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 5234,
          affiliateUrl: `https://www.amazon.com/dp/B002JAYMEE?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_protection', 'spf', 'sunscreen'],
          isDiscounted: false,
          ingredients: ['spf']
        }
      ];

      // Filter products based on specific ingredients if any are mentioned
      if (specificIngredients.length > 0) {
        const matchingProducts = sampleProducts.filter(product =>
          product.ingredients.some(ingredient =>
            specificIngredients.some(specIngredient =>
              ingredient.toLowerCase().includes(specIngredient.toLowerCase()) ||
              specIngredient.toLowerCase().includes(ingredient.toLowerCase())
            )
          )
        );

        if (matchingProducts.length > 0) {
          sampleProducts = matchingProducts.slice(0, 3);
        } else {
          sampleProducts = sampleProducts.slice(0, 3);
        }
      } else {
        sampleProducts = sampleProducts.slice(0, 3);
      }

      return NextResponse.json({
        products: sampleProducts,
        totalSelected: 3,
        factorsUsed: factors,
        message: 'Using demo products - database not configured'
      });
    }

    // Query products that match the semantic keywords
    // If beard_care is in factors, also get beard products specifically
    let products: any[] = [];

    if (factors.includes('beard_care')) {
      // First, get beard products specifically
      const { data: beardProducts, error: beardError } = await supabase
        .from('amazon_product_references')
        .select('*')
        .eq('primary_category', 'beard_care')
        .limit(10);

      if (!beardError && beardProducts) {
        products.push(...beardProducts);
      }
    }

    // Then get other products that match the semantic keywords
    const { data: otherProducts, error } = await supabase
      .from('amazon_product_references')
      .select('*')
      .overlaps('semantic_keywords', keywords)
      .neq('primary_category', 'beard_care') // Exclude beard products to avoid duplicates
      .limit(15); // Get more products for better selection

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch product recommendations' },
        { status: 500 }
      );
    }

    // Combine all products
    if (otherProducts) {
      products.push(...otherProducts);
    }

    console.log('DEBUG: Found products count:', products.length);
    console.log('DEBUG: Product categories:', products.map(p => p.primary_category));
    console.log('DEBUG: Beard products in results:', products.filter(p => p.primary_category === 'beard_care').length);

    // Prevent duplicates by ensuring max 1 product per category
    const selectedProducts: any[] = [];
    const usedCategories = new Set<string>();

    // Proper Fisher-Yates shuffle algorithm for true randomization
    const shuffled = [...products];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    // Prioritize beard products if beard_care is in factors - ALWAYS include at least one
    if (factors.includes('beard_care')) {
      const beardProducts = shuffled.filter(p => p.primary_category === 'beard_care');
      console.log('DEBUG: Found beard products:', beardProducts.length);
      console.log('DEBUG: Beard product names:', beardProducts.map(p => p.product_name));
      if (beardProducts.length > 0) {
        // Prefer traditional beard grooming products (oil, balm, kit) over growth serums
        // Exclude minoxidil products as they are growth treatments, not grooming products
        const traditionalBeardProducts = beardProducts.filter(p => {
          const name = p.product_name.toLowerCase();
          const isTraditional = (
            name.includes('oil') ||
            name.includes('balm') ||
            name.includes('kit') ||
            name.includes('wash') ||
            name.includes('conditioner')
          );
          const isGrowthTreatment = name.includes('minoxidil') || name.includes('growth serum');
          return isTraditional && !isGrowthTreatment;
        });

        const productsToSort = traditionalBeardProducts.length > 0 ? traditionalBeardProducts : beardProducts;
        const sortedBeardProducts = productsToSort.sort((a, b) => (b.review_count || 0) - (a.review_count || 0));
        selectedProducts.push(sortedBeardProducts[0]);
        usedCategories.add('beard_care');
        console.log('DEBUG: Selected beard product:', sortedBeardProducts[0].product_name);
      }
    }

    // First pass: select one product from each category (excluding already selected)
    // If beard_care was detected, ensure we have at least one beard product
    if (factors.includes('beard_care') && selectedProducts.length === 0) {
      // This shouldn't happen, but as a fallback, try to find any beard product
      const allBeardProducts = products.filter(p => p.primary_category === 'beard_care');
      if (allBeardProducts.length > 0) {
        const sortedBeardProducts = allBeardProducts.sort((a, b) => (b.review_count || 0) - (a.review_count || 0));
        selectedProducts.push(sortedBeardProducts[0]);
        usedCategories.add('beard_care');
        console.log('DEBUG: Fallback - added beard product:', sortedBeardProducts[0].product_name);
      }
    }

    // Prioritize skin care and supplements if we have space after beard products
    const priorityCategories = ['skin_care', 'supplements', 'hair_care'];

    for (const category of priorityCategories) {
      if (selectedProducts.length >= 3) break;
      if (usedCategories.has(category)) continue;

      const categoryProducts = shuffled.filter(p => p.primary_category === category);
      if (categoryProducts.length > 0) {
        // Sort by review count to get most popular
        const sortedCategoryProducts = categoryProducts.sort((a, b) => (b.review_count || 0) - (a.review_count || 0));
        selectedProducts.push(sortedCategoryProducts[0]);
        usedCategories.add(category);
      }
    }

    // Fill remaining slots with any other products
    for (const product of shuffled) {
      if (selectedProducts.length >= 3) break;

      if (!usedCategories.has(product.primary_category)) {
        selectedProducts.push(product);
        usedCategories.add(product.primary_category);
      }
    }

    // Second pass: if we still need more products, add from unused categories or different subcategories
    if (selectedProducts.length < 3) {
      for (const product of shuffled) {
        if (selectedProducts.length >= 3) break;

        // Check if this product is significantly different from already selected ones
        const isDifferent = !selectedProducts.some(selected =>
          selected.product_name.toLowerCase().includes(product.product_name.toLowerCase().split(' ')[0]) ||
          product.product_name.toLowerCase().includes(selected.product_name.toLowerCase().split(' ')[0])
        );

        if (isDifferent && !selectedProducts.find(p => p.product_id === product.product_id)) {
          selectedProducts.push(product);
        }
      }
    }

    // Transform to match expected format with affiliate links
    const curatedProducts = selectedProducts.map(product => ({
      asin: product.product_id,
      title: product.product_name,
      imageUrl: product.product_image_url,
      price: `$${(product.product_price || 0).toString()}`,
      priceBeforeDiscount: product.product_price_before_discount ? `$${(product.product_price_before_discount).toString()}` : null,
      rating: null, // We don't have rating data in our table
      reviewCount: product.review_count,
      affiliateUrl: `https://www.amazon.com/dp/${product.product_id}?tag=amzleanmed-20`,
      category: product.primary_category,
      semanticKeywords: product.semantic_keywords,
      isDiscounted: product.is_discounted
    }));

    console.log('DEBUG: Final selected products:', selectedProducts.map(p => ({ name: p.product_name, category: p.primary_category })));

    return NextResponse.json({
      products: curatedProducts,
      totalSelected: curatedProducts.length,
      factorsUsed: factors
    });

  } catch (error) {
    console.error('Curated products error:', error);
    return NextResponse.json(
      { error: 'Failed to generate product recommendations' },
      { status: 500 }
    );
  }
}
