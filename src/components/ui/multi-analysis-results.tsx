'use client';

import React, { useEffect, useState } from 'react';
import { Card } from './card';
import { Badge } from './badge';
import AttractivenessPieChart from './pie-chart';
import { Button } from './button';
import { Star, User, Ruler, Smile, Heart, ShoppingBag, ExternalLink, <PERSON>rkles, Award, Eye, Bone } from 'lucide-react';
import { formatRecommendationsAsPoints, extractKeyPhrasesForDescriptions } from '../../lib/recommendation-utils';

interface AmazonProduct {
  asin: string;
  title: string;
  imageUrl?: string;
  price?: string;
  priceBeforeDiscount?: string;
  rating?: number;
  reviewCount?: number;
  affiliateUrl: string;
  category?: string;
  semanticKeywords?: string[];
  isDiscounted?: boolean;
}

interface Factor {
  name: string;
  score: number;
  description: string;
  recommendation: string;
}

interface AnalysisItem {
  type: string;
  overall_score?: number;
  factors?: Factor[];
  analysis_text?: string;
  overall_recommendations?: string;
  shape?: string;
  confidence_score?: number;
  measurements?: any;
  styling_tips?: any;
  celebrity_examples?: string[];
  description?: string;
}

interface MultiAnalysisResult {
  analyses: AnalysisItem[];
}

interface AttractivenessResult {
  overall_score: number;
  factors: Factor[];
  analysis_text: string;
  overall_recommendations: string;
}

interface BeautyProductRecommendationsProps {
  recommendations: string;
}

interface CuratedProductRecommendationsProps {
  recommendations: string;
}

// Curated Product Recommendations - Database-driven, 3 product cards
const CuratedProductRecommendations = ({ recommendations }: CuratedProductRecommendationsProps) => {
  const [products, setProducts] = useState<AmazonProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [keyPhrases, setKeyPhrases] = useState<string[]>([]);

  useEffect(() => {
    if (recommendations && recommendations.length > 20) {
      // Extract key phrases from recommendations for better product descriptions
      const phrases = extractKeyPhrasesForDescriptions(recommendations);
      setKeyPhrases(phrases);

      const fetchProducts = async () => {
        setLoading(true);
        setError(null);
        try {
          const response = await fetch('/api/curated-products', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ recommendations }),
          });
          const data = await response.json();
          if (data.error) {
            setError(data.error);
          } else {
            setProducts(data.products || []);
          }
        } catch (err) {
          setError('Failed to load product recommendations');
        } finally {
          setLoading(false);
        }
      };
      fetchProducts();
    }
  }, [recommendations]);

  if (!recommendations || recommendations.length <= 20) {
    return null;
  }

  // Show loading state
  if (loading) {
    return (
      <Card className="p-6 bg-gradient-to-r from-slate-50 via-gray-50 to-blue-50 border-2 border-slate-300">
        <div className="text-center">
          <ShoppingBag className="h-10 w-10 text-slate-700 mx-auto mb-3" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Loading Product Recommendations...</h3>
          <p className="text-gray-600">Finding products tailored to your analysis</p>
        </div>
      </Card>
    );
  }

  // Show error state (for debugging)
  if (error) {
    return (
      <Card className="p-6 bg-red-50 border-2 border-red-200">
        <div className="text-center">
          <h3 className="text-xl font-bold text-red-900 mb-2">Error Loading Products</h3>
          <p className="text-red-600">{error}</p>
        </div>
      </Card>
    );
  }

  // Don't show if no products found
  if (products.length === 0) {
    return null;
  }

  // Create engaging product benefits based on keywords and recommendation phrases
  const getProductBenefits = (keywords: string[]) => {
    const benefits = [];

    // Use key phrases from recommendations for more relevant descriptions
    if (keyPhrases.includes('antioxidant protection') && keywords.some(k => k.includes('vitamin') || k.includes('antioxidant'))) {
      benefits.push("✨ Antioxidant protection and brightening");
    } else if (keywords.some(k => k.includes('skin_radiance') || k.includes('vitamin'))) {
      benefits.push("✨ Enhanced skin radiance and glow");
    }

    if (keyPhrases.includes('hydration') && keywords.some(k => k.includes('hydration') || k.includes('hyaluronic'))) {
      benefits.push("💧 Deep hydration and moisture retention");
    } else if (keywords.some(k => k.includes('hydration'))) {
      benefits.push("💧 Deep skin hydration and moisture");
    }

    if (keyPhrases.includes('firmness') || keyPhrases.includes('elasticity')) {
      if (keywords.some(k => k.includes('collagen') || k.includes('elasticity'))) {
        benefits.push("🔄 Improved skin firmness and elasticity");
      }
    } else if (keywords.some(k => k.includes('collagen') || k.includes('elasticity'))) {
      benefits.push("🔄 Improved skin elasticity and firmness");
    }

    if (keyPhrases.includes('protection') && keywords.some(k => k.includes('spf') || k.includes('protection'))) {
      benefits.push("🛡️ Daily protection and care");
    }

    if (keywords.some(k => k.includes('bone') || k.includes('facial_structure'))) {
      benefits.push("🦴 Stronger facial bone structure");
    }

    if (keywords.some(k => k.includes('eye') || k.includes('ocular'))) {
      benefits.push("👀 Better eye vitality and brightness");
    }

    // Return first 2-3 benefits for display
    return benefits.slice(0, 2);
  };

  const getProductIcon = (category: string) => {
    switch (category) {
      case 'skin_care': return <Sparkles className="h-5 w-5 text-pink-500" />;
      case 'hair_care': return <Award className="h-5 w-5 text-purple-500" />;
      case 'eye_care': return <Eye className="h-5 w-5 text-blue-500" />;
      case 'health_wellness': return <Bone className="h-5 w-5 text-green-500" />;
      default: return <Heart className="h-5 w-5 text-pink-500" />;
    }
  };

  return (
    <Card className="p-6 bg-gradient-to-r from-slate-50 via-gray-50 to-blue-50 border-2 border-slate-300">
      <div className="text-center mb-6">
        <ShoppingBag className="h-10 w-10 text-slate-700 mx-auto mb-3" />
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          ⭐ Personalized Recommendations Based on Your Analysis
        </h3>
        <p className="text-gray-600">
          Products designed to address your specific attractiveness goals
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {products.map((product, index) => {
          const benefits = getProductBenefits(product.semanticKeywords || []);
          const icon = getProductIcon(product.category || 'skin_care');

          return (
            <Card key={product.asin} className="bg-white shadow-xl hover:shadow-2xl transition-all duration-300 border-0 overflow-hidden">
              {/* Product Image */}
              <div className="relative">
                {product.imageUrl && (
                  <div className="h-40 bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
                    <img
                      src={product.imageUrl}
                      alt={product.title}
                      className="max-h-full max-w-full object-contain hover:scale-105 transition-transform duration-200"
                      onError={(e) => (e.target as HTMLImageElement).src = '/placeholder-product.png'}
                    />
                  </div>
                )}
                <div className="absolute top-2 right-2 bg-pink-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                  {index + 1}
                </div>
              </div>

              {/* Product Content */}
              <div className="p-5">
                {/* Category Badge */}
                <div className="flex items-center gap-2 mb-3">
                  {icon}
                  <Badge variant="secondary" className="text-xs bg-pink-100 text-pink-700">
                    {product.category === 'skin_care' ? 'Skin Health' :
                     product.category === 'hair_care' ? 'Hair Growth' :
                     product.category === 'eye_care' ? 'Eye Health' :
                     'Wellness'}
                  </Badge>
                </div>

                {/* Product Title */}
                <h4 className="font-bold text-lg mb-3 line-clamp-2 text-gray-900 hover:text-pink-600 transition-colors">
                  {product.title}
                </h4>

                {/* Benefits */}
                {benefits.length > 0 && (
                  <div className="mb-4 space-y-1">
                    {benefits.map((benefit, i) => (
                      <p key={i} className="text-sm text-gray-700 flex items-start gap-1">
                        {benefit}
                      </p>
                    ))}
                  </div>
                )}

                {/* Price */}
                <div className="mb-4">
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold text-green-600">
                      {product.price}
                    </span>
                    {product.priceBeforeDiscount && (
                      <span className="text-sm text-gray-500 line-through">
                        {product.priceBeforeDiscount}
                      </span>
                    )}
                  </div>
                  {product.isDiscounted && (
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                      ON SALE! ✨
                    </span>
                  )}
                </div>

                {/* Reviews if available */}
                {product.reviewCount && (
                  <div className="flex items-center gap-1 mb-4 text-sm text-gray-600">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{product.reviewCount} happy customers</span>
                  </div>
                )}

                {/* CTA Button */}
                <Button
                  onClick={() => {
                    // Track product click with Umami
                    if (typeof window !== 'undefined' && (window as any).umami) {
                      (window as any).umami.track('product-click', {
                        product_name: product.title,
                        product_asin: product.asin,
                        product_category: product.category,
                        product_price: product.price,
                        position: index + 1
                      });
                    }
                    window.open(product.affiliateUrl, '_blank');
                  }}
                  className="w-full bg-gradient-to-r from-slate-700 to-blue-700 hover:from-slate-800 hover:to-blue-800 text-white font-bold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all"
                >
                  <ExternalLink className="mr-2 h-5 w-5" />
                  Shop This Product
                </Button>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Affiliate Disclosure */}
      <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 text-center border border-slate-300">
        <p className="text-sm text-gray-700 leading-relaxed">
          <Heart className="h-4 w-4 inline text-slate-600 mr-1" />
          As an Amazon Associate, we earn small commissions from qualifying purchases made through these links at no extra cost to you.
          These recommendations are tailored to help you achieve your attractiveness goals based on your analysis.
        </p>
      </div>
    </Card>
  );
};

const BeautyProductRecommendations = ({ recommendations }: BeautyProductRecommendationsProps) => {
  const [products, setProducts] = useState<AmazonProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (recommendations && recommendations.length > 20) {
      const fetchProducts = async () => {
        setLoading(true);
        setError(null);
        try {
          const response = await fetch('/api/beauty-recommendations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ recommendations }),
          });
          const data = await response.json();
          if (data.error) {
            setError(data.error);
          } else {
            setProducts(data.products || []);
          }
        } catch (err) {
          setError('Failed to load product recommendations');
        } finally {
          setLoading(false);
        }
      };
      fetchProducts();
    }
  }, [recommendations]);

  if (!recommendations || recommendations.length <= 20) {
    return null;
  }

  return (
    <Card className="p-6">
      <h3 className="text-2xl font-bold mb-4 flex items-center gap-3">
        <ShoppingBag className="h-6 w-6 text-pink-500" />
        Beauty Product Recommendations
      </h3>

      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
          <span className="ml-3">Finding products...</span>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-600">
          {error}
        </div>
      )}

      {!loading && !error && products.length === 0 && (
        <p className="text-gray-500">No product recommendations available at the moment.</p>
      )}

      {!loading && !error && products.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {products.map((product, index) => (
            <Card key={product.asin || index} className="p-4 hover:shadow-lg transition-shadow">
              {product.imageUrl && (
                <div className="mb-3">
                  <img
                    src={product.imageUrl}
                    alt={product.title}
                    className="w-full h-32 object-contain rounded-lg"
                    onError={(e) => (e.target as HTMLImageElement).src = '/placeholder-product.png'}
                  />
                </div>
              )}
              <h4 className="font-semibold text-sm mb-2 line-clamp-2">{product.title}</h4>
              {product.price && <p className="text-lg font-bold text-pink-600 mb-2">{product.price}</p>}
              {product.rating && (
                <div className="flex items-center mb-3">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="ml-1 text-sm">{product.rating.toFixed(1)} ({product.reviewCount})</span>
                </div>
              )}
              <Button
                onClick={() => {
                  // Track product click with Umami
                  if (typeof window !== 'undefined' && (window as any).umami) {
                    (window as any).umami.track('product-click', {
                      product_name: product.title,
                      product_asin: product.asin,
                      product_category: product.category,
                      product_price: product.price,
                      position: index + 1,
                      source: 'beauty-recommendations'
                    });
                  }
                  window.open(product.affiliateUrl, '_blank');
                }}
                size="sm"
                className="w-full bg-gradient-to-r from-slate-700 to-blue-700 hover:from-slate-800 hover:to-blue-800"
              >
                Buy Now
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </Card>
          ))}
        </div>
      )}

      {!loading && (
        <div className="mt-4 p-3 bg-gradient-to-r from-slate-50 to-gray-50 rounded-lg">
          <p className="text-sm text-gray-600">
            These recommendations are based on your analysis. Links are affiliate links that support the project.
          </p>
        </div>
      )}
    </Card>
  );
};

interface MultiAnalysisResultsProps {
  data: AttractivenessResult | MultiAnalysisResult | null;
  uploadedImage?: string;
  isSharedView?: boolean;
}

const getAnalysisTitle = (type: string): string => {
  const titles: { [key: string]: string } = {
    'attractive-score': 'Attractiveness Analysis',
    'face-shape-detector': 'Face Shape Analysis',
    'golden-ratio-face': 'Golden Ratio Analysis',
    'smile-rating': 'Smile Analysis'
  };
  return titles[type] || type;
};

const getAnalysisIcon = (type: string) => {
  const icons: { [key: string]: React.ReactNode } = {
    'attractive-score': <Star className="h-5 w-5" />,
    'face-shape-detector': <User className="h-5 w-5" />,
    'golden-ratio-face': <Ruler className="h-5 w-5" />,
    'smile-rating': <Smile className="h-5 w-5" />
  };
  return icons[type] || <Heart className="h-5 w-5" />;
};

const FaceShapeAnalysis = ({ analysis }: { analysis: AnalysisItem }) => (
  <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
    <h3 className="text-3xl font-bold text-gray-900 mb-6">Face Shape Analysis</h3>
    <div className="space-y-6">
      <div className="text-center">
        <div className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 mb-2">
          {analysis.shape}
        </div>
        {analysis.confidence_score && (
          <p className="text-lg text-gray-600">
            Confidence: {(analysis.confidence_score * 100).toFixed(1)}%
          </p>
        )}
      </div>

      {analysis.description && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Description:</h4>
          <p className="text-gray-600">{analysis.description}</p>
        </div>
      )}

      {analysis.styling_tips && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Styling Tips:</h4>
          <div className="space-y-3">
            {Object.entries(analysis.styling_tips).map(([key, value]) => (
              <div key={key} className="flex flex-col sm:flex-row sm:items-center gap-2">
                <span className="font-medium text-gray-900 capitalize min-w-0 sm:min-w-[120px]">
                  {key.replace('_', ' ')}:
                </span>
                <span className="text-gray-600 flex-1">{value as string}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {analysis.celebrity_examples && analysis.celebrity_examples.length > 0 && (
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Celebrity Examples:</h4>
          <div className="flex flex-wrap gap-2">
            {analysis.celebrity_examples.map((celebrity, index) => (
              <span key={index} className="px-3 py-1 bg-white rounded-full text-sm font-medium text-gray-700 shadow-sm">
                {celebrity}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  </div>
);

const GoldenRatioAnalysis = ({ analysis }: { analysis: AnalysisItem }) => (
  <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
    <h3 className="text-3xl font-bold text-gray-900 mb-6">Golden Ratio Analysis</h3>
    <div className="space-y-6">
      {analysis.overall_score && (
        <div className="text-center">
          <div className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 mb-2">
            {analysis.overall_score.toFixed(1)}/10
          </div>
          <p className="text-lg text-gray-600">Golden Ratio Score</p>
        </div>
      )}

      {analysis.analysis_text && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Analysis:</h4>
          <p className="text-gray-600 italic">&ldquo;{analysis.analysis_text}&rdquo;</p>
        </div>
      )}

      {analysis.measurements && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Measurements:</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {Object.entries(analysis.measurements).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center p-2 bg-white rounded">
                <span className="capitalize text-gray-700">{key.replace('_', ' ')}:</span>
                <span className="font-medium text-gray-900">{value as string}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {analysis.overall_recommendations && (
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Overall Recommendations:</h4>
          <div className="space-y-2">
            {formatRecommendationsAsPoints(analysis.overall_recommendations).map((point, index) => (
              <div key={index} className="flex items-start gap-2">
                <span className="font-semibold text-pink-600 min-w-[24px]">{index + 1})</span>
                <span className="text-gray-700 flex-1">{point}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  </div>
);

export default function MultiAnalysisResults({ data, uploadedImage, isSharedView = false }: MultiAnalysisResultsProps) {
  if (!data) return null;

  // Handle single analysis (backward compatibility)
  const isSingleAnalysis = 'factors' in data;
  if (isSingleAnalysis) {
    return (
      <div className="space-y-8">
        <AttractivenessPieChart data={data} uploadedImage={uploadedImage} />
        {/* Curated Product Recommendations for single analysis */}
        {(data as AttractivenessResult).overall_recommendations?.length > 20 && (
          <CuratedProductRecommendations
            recommendations={(data as AttractivenessResult).overall_recommendations}
          />
        )}
      </div>
    );
  }

  // Handle multi-analysis
  const multiData = data as MultiAnalysisResult;
  const analyses = multiData.analyses;

  // Check if analyses array exists and has items
  if (!analyses || !Array.isArray(analyses) || analyses.length === 0) {
    return <div>No analysis data available</div>;
  }

  return (
    <div className="space-y-8">
      {analyses.map((analysis, index) => {
        switch (analysis.type) {
          case 'attractive-score':
            // Convert to single analysis format for the pie chart
            const attractiveData: AttractivenessResult = {
              overall_score: analysis.overall_score || 0,
              factors: analysis.factors || [],
              analysis_text: analysis.analysis_text || '',
              overall_recommendations: analysis.overall_recommendations || ''
            };
            return (
              <div key={index}>
                <AttractivenessPieChart data={attractiveData} uploadedImage={uploadedImage} />
              </div>
            );

          case 'face-shape-detector':
            return <FaceShapeAnalysis key={index} analysis={analysis} />;

          case 'golden-ratio-face':
            return <GoldenRatioAnalysis key={index} analysis={analysis} />;

          default:
            return (
              <Card key={index} className="p-6">
                <h3 className="text-2xl font-bold mb-4 flex items-center gap-3">
                  {getAnalysisIcon(analysis.type)}
                  {getAnalysisTitle(analysis.type)}
                </h3>
                <div className="space-y-4">
                  {analysis.overall_score && (
                    <div className="text-center">
                      <div className="text-4xl font-bold text-primary mb-2">
                        {analysis.overall_score.toFixed(1)}/10
                      </div>
                    </div>
                  )}
                  {analysis.analysis_text && (
                    <p className="text-muted-foreground italic">&ldquo;{analysis.analysis_text}&rdquo;</p>
                  )}
                  {analysis.overall_recommendations && (
                    <div>
                      <h4 className="font-semibold mb-3">Overall Recommendations:</h4>
                      <div className="space-y-2">
                        {formatRecommendationsAsPoints(analysis.overall_recommendations).map((point, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <span className="font-semibold text-pink-600 min-w-[24px]">{index + 1})</span>
                            <span className="text-muted-foreground flex-1">{point}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            );
        }
      })}

      {/* Curated Product Recommendations - NEW DATABASE-DRIVEN PRODUCTS */}
      {analyses.some(analysis => analysis.overall_recommendations && analysis.overall_recommendations.length > 20) && (
        <CuratedProductRecommendations
          recommendations={analyses.find(analysis => analysis.overall_recommendations)?.overall_recommendations || ''}
        />
      )}
    </div>
  );
}
